{"mcpServers": {"gitlab": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gitlab"], "env": {"GITLAB_PERSONAL_ACCESS_TOKEN": "**************************", "GITLAB_API_URL": "https://iotgit.leedarson.com/api/v4"}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "D:\\workspace\\java\\blysin-test\\.comate\\memory.json"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}}}
package com.blysin.blysintest.po2.service;

import com.blysin.blysintest.po2.entity.CallOffOrder;
import com.blysin.blysintest.po2.entity.DemandOrder;
import com.blysin.blysintest.po2.strategy.LockDateStrategy;
import com.blysin.blysintest.po2.strategy.LockDateStrategyFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class CallOffCalculationServiceTest {
    @Mock
    private LockDateStrategyFactory lockDateStrategyFactory;
    
    @InjectMocks
    private CallOffCalculationService service;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(lockDateStrategyFactory.getStrategy(anyString()))
            .thenReturn(new TestLockDateStrategy());
    }

    // 测试需求单为空的情况
    @Test
    void testProcessDailyData_EmptyDemands() {
        List<CallOffOrder> result = service.calculateCallOffPlan(
            Collections.emptyList(),
            new ArrayList<>(),
            LocalDate.now()
        );
        assertTrue(result.isEmpty());
    }

    // 测试最后需求日期早于当前日期的情况
    @Test
    void testProcessDailyData_LastDemandDateBeforeCurrent() {
        List<DemandOrder> demands = List.of(
            buildDemandOrder(LocalDate.now().minusDays(3), 100)
        );
        
        List<CallOffOrder> result = service.calculateCallOffPlan(
            demands,
            new ArrayList<>(),
            LocalDate.now()
        );
        assertTrue(result.isEmpty());
    }

    // 测试当天总需求为0的情况
    @Test
    void testProcessDailyData_ZeroTotalDemand() {
        List<DemandOrder> demands = List.of(
            buildDemandOrder(LocalDate.now().plusDays(1), 0),
            buildDemandOrder(LocalDate.now().plusDays(1), 0)
        );
        
        List<CallOffOrder> result = service.calculateCallOffPlan(
            demands,
            new ArrayList<>(),
            LocalDate.now()
        );
        assertTrue(result.isEmpty());
    }

    // 测试正需求且存在未锁定叫料单的情况
    @Test
    void testProcessDailyData_PositiveDemandWithExistingCallOff() {
        List<DemandOrder> demands = List.of(
            buildDemandOrder(LocalDate.now().plusDays(1), 200)
        );
        
        List<CallOffOrder> existingCallOffs = List.of(
            buildCallOffOrder(LocalDate.now().plusDays(1), 150, false)
        );
        
        List<CallOffOrder> result = service.calculateCallOffPlan(
            demands,
            existingCallOffs,
            LocalDate.now()
        );
        assertEquals(1, result.size());
        assertEquals(200 - 150, result.get(0).getCallOffQuantity());
    }

    // 测试正需求且无叫料单的情况
    @Test
    void testProcessDailyData_PositiveDemandNoCallOff() {
        List<DemandOrder> demands = List.of(
            buildDemandOrder(LocalDate.now().plusDays(1), 300)
        );
        
        List<CallOffOrder> result = service.calculateCallOffPlan(
            demands,
            new ArrayList<>(),
            LocalDate.now()
        );
        assertEquals(1, result.size());
        assertEquals(300, result.get(0).getCallOffQuantity());
    }

    // 测试负需求且有剩余正需求的情况
    @Test
    void testProcessDailyData_NegativeDemandWithRemainingPositive() {
        List<DemandOrder> demands = new ArrayList<>(List.of(
            buildDemandOrder(LocalDate.now().plusDays(1), -100),
            buildDemandOrder(LocalDate.now().plusDays(2), 50)
        ));
        
        List<CallOffOrder> result = service.calculateCallOffPlan(
            demands,
            new ArrayList<>(),
            LocalDate.now()
        );
        assertEquals(1, result.size());
        assertEquals(100, result.get(0).getCallOffQuantity());
    }

    // 测试负需求且无剩余正需求的情况
    @Test
    void testProcessDailyData_NegativeDemandNoRemaining() {
        List<DemandOrder> demands = List.of(
            buildDemandOrder(LocalDate.now().plusDays(1), -100),
            buildDemandOrder(LocalDate.now().plusDays(2), -50)
        );
        
        List<CallOffOrder> result = service.calculateCallOffPlan(
            demands,
            new ArrayList<>(),
            LocalDate.now()
        );
        assertTrue(result.isEmpty());
    }

    private DemandOrder buildDemandOrder(LocalDate date, int qty) {
        return DemandOrder.builder()
            .demandDate(date)
            .demandQuantity(qty)
            .materialId("MAT001")
            .supplier("SUP001")
            .factoryCode("FACT001")
            .transportType("TRUCK")
            .build();
    }

    private CallOffOrder buildCallOffOrder(LocalDate date, int qty, boolean locked) {
        return CallOffOrder.builder()
            .callOffDate(date)
            .callOffQuantity(qty)
            .locked(locked)
            .materialId("MAT001")
            .supplier("SUP001")
            .factoryNo("FACT001")
            .build();
    }

    static class TestLockDateStrategy implements LockDateStrategy {
        @Override
        public LocalDate calculateLockDate(DemandOrder order, LocalDate currentDate) {
            return currentDate.plusDays(3);
        }
    }
} 
package com.blysin.blysintest;

import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

public class SimpleTest {
    @Test
    @SneakyThrows
    public void testMapModify() {
        Map<String, Integer> map = new TreeMap<>();
        for (int i = 0; i < 5; i++) {
            if (i == 2) {
                continue;
            }
            map.put("key" + i, i);
        }

        map.forEach((key, value) -> {
            if (value == 0) {
                map.put("key2", 1234);
            }
            System.out.println(key + " " + value);
        });
    }
}

package com.blysin.blysintest.po2.entity;

import com.blysin.blysintest.po2.enums.TransportType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 叫料单实体
 *
 * <AUTHOR> href=mailto:<EMAIL>" rel="nofollow">daish<PERSON>kun</a>
 * @since 2025/6/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CallOffOrder {
    /**
     * 叫料日期
     */
    private LocalDate callOffDate;

    /**
     * 叫料数量
     */
    private Integer callOffQuantity;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 需求日期
     */
    private LocalDate demandDate;

    /**
     * 物料ID
     */
    private String materialId;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 工厂编号
     */
    private String factoryNo;

    /**
     * 是否锁定
     * 计算时使用
     */
    private Boolean locked;

    /**
     * 入库数量
     */
    private Integer receivedQuantity;

    /**
     * 运输方式
     */
    private TransportType transportType;

    /**
     * 运输时间
     * PO2-天
     * JIT-小时
     */
    private Integer transportTime;
} 
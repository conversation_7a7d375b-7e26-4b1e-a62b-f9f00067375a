package com.blysin.blysintest.po2.service;

import com.blysin.blysintest.po2.entity.CallOffOrder;
import com.blysin.blysintest.po2.entity.DemandOrder;
import com.blysin.blysintest.po2.strategy.LockDateStrategy;
import com.blysin.blysintest.po2.strategy.LockDateStrategyFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 叫料计算服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CallOffCalculationService {
    private final LockDateStrategyFactory lockDateStrategyFactory;

    /**
     * 计算叫料计划
     *
     * @param demandOrders  需求单列表
     * @param callOffOrders 叫料单列表
     * @param currentDate   当前日期
     * @return 计算后的叫料单列表
     */
    public List<CallOffOrder> calculateCallOffPlan(List<DemandOrder> demandOrders,
                                                   List<CallOffOrder> callOffOrders,
                                                   LocalDate currentDate) {
        // 参数校验
        if (demandOrders == null || callOffOrders == null || currentDate == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        if (currentDate.isAfter(LocalDate.now())) {
            throw new IllegalArgumentException("当前日期不能大于系统日期");
        }

        // 1. 获取前一天的叫料单并处理
        processPreviousDayCallOffs(callOffOrders, demandOrders, currentDate);

        // 2. 按工厂+供应商+物料ID分组
        Map<String, List<DemandOrder>> demandGroups = demandOrders.stream()
                .collect(Collectors.groupingBy(this::getGroupKey));
        Map<String, List<CallOffOrder>> callOffGroups = callOffOrders.stream()
                .collect(Collectors.groupingBy(this::getGroupKey));

        // 3. 对每个分组进行计算
        List<CallOffOrder> result = new ArrayList<>();
        for (String groupKey : demandGroups.keySet()) {
            List<DemandOrder> groupDemands = demandGroups.get(groupKey);
            List<CallOffOrder> groupCallOffs = callOffGroups.getOrDefault(groupKey, new ArrayList<>());

            // 4. 计算锁定日期
            calculateLockDates(groupDemands, currentDate);

            // 5. 处理锁定状态
            processLockedCallOffs(groupCallOffs, groupDemands, currentDate);

            // 6. 按天处理数据
            result.addAll(processDailyData(groupDemands, groupCallOffs, currentDate));
        }

        return result;
    }

    /**
     * 处理前一天的叫料单
     */
    private void processPreviousDayCallOffs(List<CallOffOrder> callOffOrders,
                                            List<DemandOrder> demandOrders,
                                            LocalDate currentDate) {
        LocalDate previousDay = currentDate.minusDays(1);

        // 获取前一天的叫料单
        List<CallOffOrder> previousDayCallOffs = callOffOrders.stream()
                .filter(c -> c.getCallOffDate() != null && c.getCallOffDate().equals(previousDay))
                .collect(Collectors.toList());

        // 处理入库数量小于叫料数量的情况
        for (CallOffOrder callOff : previousDayCallOffs) {
            if (callOff.getReceivedQuantity() == null) {
                callOff.setReceivedQuantity(0);
            }
            if (callOff.getCallOffQuantity() == null) {
                callOff.setCallOffQuantity(0);
            }
            if (callOff.getReceivedQuantity() < callOff.getCallOffQuantity()) {
                // 创建虚拟需求单
                DemandOrder virtualDemand = createVirtualDemand(
                        callOff,
                        currentDate,
                        callOff.getCallOffQuantity() - callOff.getReceivedQuantity()
                );
                demandOrders.add(virtualDemand);
            }
        }
    }

    /**
     * 处理锁定状态
     */
    private void processLockedCallOffs(List<CallOffOrder> callOffOrders,
                                       List<DemandOrder> demandOrders,
                                       LocalDate currentDate) {
        // 删除非锁定状态的叫料单
        callOffOrders.removeIf(callOff -> !callOff.getLocked());

        // 更新锁定状态
        for (CallOffOrder callOff : callOffOrders) {
            // 查找对应的需求单
            Optional<DemandOrder> demand = demandOrders.stream()
                    .filter(d -> d.getOrderNo().equals(callOff.getOrderNo()))
                    .findFirst();

            if (demand.isPresent()) {
                // 如果需求单的锁定日期<=今天，则不允许变更
                if (demand.get().getLockDate() != null &&
                        !demand.get().getLockDate().isAfter(currentDate)) {
                    callOff.setLocked(true);
                }
            }
        }

        // 删除非锁定状态的叫料单
        callOffOrders.removeIf(callOff -> !callOff.getLocked());
    }

    /**
     * 获取分组键
     */
    private String getGroupKey(DemandOrder order) {
        return String.format("%s_%s_%s", order.getFactoryCode(), order.getSupplier(), order.getMaterialId());
    }

    /**
     * 获取分组键
     */
    private String getGroupKey(CallOffOrder order) {
        return String.format("%s_%s_%s", order.getFactoryNo(), order.getSupplier(), order.getMaterialId());
    }

    /**
     * 计算锁定日期
     */
    private void calculateLockDates(List<DemandOrder> demandOrders, LocalDate currentDate) {
        for (DemandOrder order : demandOrders) {
            LockDateStrategy strategy = lockDateStrategyFactory.getStrategy(order.getTransportType());
            order.setLockDate(strategy.calculateLockDate(order, currentDate));
        }
    }

    /**
     * 按天处理数据
     */
    private List<CallOffOrder> processDailyData(List<DemandOrder> demandOrders,
                                                List<CallOffOrder> callOffOrders,
                                                LocalDate currentDate) {
        List<CallOffOrder> result = new ArrayList<>();
        log.info("开始按天处理数据，当前日期: {}，需求单数量: {}，叫料单数量: {}", currentDate, demandOrders.size(), callOffOrders.size());

        if (demandOrders.isEmpty()) {
            log.info("需求单列表为空，直接返回空结果");
            return result;
        }

        // 获取最后的需求日期
        LocalDate lastDemandDate = demandOrders.get(demandOrders.size() - 1).getDemandDate();
        log.info("最后需求日期: {}", lastDemandDate);

        // 增加边界检查：如果最后需求日期早于当前日期，直接返回空结果
        if (lastDemandDate.isBefore(currentDate)) {
            log.warn("最后需求日期{}早于当前日期{}，不进行计算", lastDemandDate, currentDate);
            return result;
        }

        // 准备数据，例如排序、过滤等
        prepareData(demandOrders, callOffOrders);

        // 从当前日期开始，按天处理
        LocalDate processDate = currentDate;
        while (!processDate.isAfter(lastDemandDate)) {
            final LocalDate currentProcessDate = processDate;
            log.info("正在处理日期: {}", currentProcessDate);

            // 获取当天的需求单
            List<DemandOrder> dailyDemands = demandOrders.stream()
                    .filter(d -> d.getDemandDate().equals(currentProcessDate))
                    .collect(Collectors.toList());
            log.info("当天{}有{}条需求单", currentProcessDate, dailyDemands.size());

            if (!dailyDemands.isEmpty()) {
                // 计算当天总需求数量
                int totalDemand = dailyDemands.stream()
                        .mapToInt(DemandOrder::getDemandQuantity)
                        .sum();

                if (totalDemand == 0) {
                    // 如果当天总需求数量为0，则直接跳过
                    processDate = processDate.plusDays(1);
                    continue;
                }

                if (totalDemand > 0) {
                    // 获取当天的叫料单
                    Optional<CallOffOrder> dailyCallOff = callOffOrders.stream()
                            .filter(c -> c.getCallOffDate().equals(currentProcessDate))
                            .findFirst();

                    if (dailyCallOff.isPresent()) {
                        // 如果存在叫料单，检查是否锁定
                        CallOffOrder callOff = dailyCallOff.get();
                        if (!callOff.getLocked()) {
                            // 计算差额
                            int difference = totalDemand - intVal(callOff.getCallOffQuantity());

                            if (difference != 0) {
                                // 创建虚拟需求单
                                lastDemandDate = createVirtualDemand(demandOrders, dailyDemands, currentProcessDate, difference, lastDemandDate);
                            }
                        }
                    } else {
                        // 如果不存在叫料单，创建新的叫料单
                        CallOffOrder newCallOff = createCallOffOrder(
                                dailyDemands.get(0),
                                currentProcessDate,
                                totalDemand
                        );
                        log.info("创建新叫料单，日期: {}，数量: {}", currentProcessDate, totalDemand);
                        result.add(newCallOff);
                    }
                } else {
                    // 需求数量为负数的情况
                    // 判断剩余未处理的需求是不是都为负数，如果是直接跳出循环，如果否则创建虚拟的需求单，传递给下一次循环处理

                    //获取剩余的需求
                    boolean hasRemainingDemand = dailyDemands.stream()
                            .anyMatch(d -> d.getDemandDate().isAfter(currentProcessDate) && d.getDemandQuantity() > 0);
                    if (!hasRemainingDemand) {
                        // 所有剩余需求都为负数，跳出循环
                        break;
                    }

                    // 修改负数处理逻辑，创建绝对值的虚拟需求单
                    int virtualQty = Math.abs(totalDemand);
                    lastDemandDate = createVirtualDemand(demandOrders, dailyDemands, currentProcessDate, virtualQty, lastDemandDate);
                    log.info("处理负数需求，虚拟数量: {}，新最后需求日期: {}", virtualQty, lastDemandDate);
                }
            }

            processDate = processDate.plusDays(1);
        }

        log.info("按天处理完成，共生成{}条叫料单", result.size());
        return result;
    }

    /**
     * 准备数据，例如排序、过滤等
     */
    private static void prepareData(List<DemandOrder> demandOrders, List<CallOffOrder> callOffOrders) {
        int demandSize = demandOrders.size();
        int callOffSize = callOffOrders.size();
        log.info("数据准备前, demand orders: {}, call off orders: {}", demandSize, callOffSize);
        //去掉需求日期为空的数据，以免出现NullPointerException
        boolean invalidDemand = demandOrders.removeIf(d -> d.getDemandDate() == null);
        if (invalidDemand) {
            log.warn("移除了{}条需求日期为空的需求单", demandSize - demandOrders.size());
        }

        boolean invalidCallOff = callOffOrders.removeIf(c -> c.getCallOffDate() == null);
        if (invalidCallOff) {
            log.warn("移除了{}条叫料日期为空的叫料单", callOffSize - callOffOrders.size());
        }

        //设置需求数量，防止空指针
        demandOrders.forEach(d -> d.setDemandQuantity(intVal(d.getDemandQuantity())));


        // 按需求日期排序
        demandOrders.sort(Comparator.comparing(DemandOrder::getDemandDate));
        callOffOrders.sort(Comparator.comparing(CallOffOrder::getCallOffDate));
        log.info("完成数据准备, 剩下 demand orders: {}, call off orders: {}", demandOrders.size(), callOffOrders.size());
    }

    private LocalDate createVirtualDemand(List<DemandOrder> demandOrders, List<DemandOrder> dailyDemands, LocalDate currentProcessDate, int difference, LocalDate lastDemandDate) {
        DemandOrder target = maxRequirement(dailyDemands);
        DemandOrder virtualDemand = createVirtualDemand(
                target,
                currentProcessDate.plusDays(1),
                difference
        );
        demandOrders.add(virtualDemand);

        // 更新最后的需求日期
        if (virtualDemand.getDemandDate().isAfter(lastDemandDate)) {
            lastDemandDate = virtualDemand.getDemandDate();
        }
        return lastDemandDate;
    }

    private DemandOrder maxRequirement(List<DemandOrder> dailyDemands) {
        return dailyDemands.stream()
                .max(Comparator.comparingInt(d -> intVal(d.getDemandQuantity())))
                .orElseThrow(() -> new IllegalArgumentException("需求列表为空"));
    }

    private static int intVal(Integer intObj) {
        return intObj != null ? intObj : 0;
    }

    /**
     * 创建虚拟需求单
     */
    private DemandOrder createVirtualDemand(DemandOrder template, LocalDate demandDate, int quantity) {
        return DemandOrder.builder()
                .orderNo(template.getOrderNo())
                .demandDate(demandDate)
                .actualDemandDate(demandDate)
                .materialId(template.getMaterialId())
                .supplier(template.getSupplier())
                .factoryCode(template.getFactoryCode())
                .demandQuantity(quantity)
                .transportType(template.getTransportType())
                .transportTime(template.getTransportTime())
                .build();
    }

    /**
     * 创建虚拟需求单（从叫料单创建）
     */
    private DemandOrder createVirtualDemand(CallOffOrder template, LocalDate demandDate, int quantity) {
        return DemandOrder.builder()
                .orderNo(template.getOrderNo())
                .demandDate(demandDate)
                .actualDemandDate(demandDate)
                .materialId(template.getMaterialId())
                .supplier(template.getSupplier())
                .factoryCode(template.getFactoryNo())
                .demandQuantity(quantity)
                .transportType(template.getTransportType())
                .transportTime(template.getTransportTime())
                .build();
    }

    /**
     * 创建叫料单
     */
    private CallOffOrder createCallOffOrder(DemandOrder demand, LocalDate callOffDate, int quantity) {
        return CallOffOrder.builder()
                .callOffDate(callOffDate)
                .callOffQuantity(quantity)
                .orderNo(demand.getOrderNo())
                .demandDate(demand.getDemandDate())
                .materialId(demand.getMaterialId())
                .supplier(demand.getSupplier())
                .factoryNo(demand.getFactoryCode())
                .transportType(demand.getTransportType())
                .transportTime(demand.getTransportTime())
                .locked(false)
                .receivedQuantity(0)
                .build();
    }
} 
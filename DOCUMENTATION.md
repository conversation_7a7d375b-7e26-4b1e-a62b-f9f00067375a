# blysin-test 项目文档

## 项目概述
- 基于Spring Boot 4.0.0-SNAPSHOT的Java 21项目
- 物料叫料计划计算系统
- 核心功能：根据需求单计算叫料计划，考虑运输方式和锁定规则

## 核心业务实体
### DemandOrder (需求单)
- 字段：orderNo, demandDate, materialId, supplier, factoryCode
- 运输相关：transportType, transportTime
- 锁定日期：lockDate

### CallOffOrder (叫料单)
- 字段：callOffDate, callOffQuantity, orderNo
- 状态：locked, receivedQuantity
- 关联需求单信息

## 策略模式实现
### LockDateStrategyFactory
- 根据运输类型获取对应策略
- 初始化时建立运输类型到策略的映射

### LockDateStrategy 接口
- calculateLockDate(DemandOrder, LocalDate): LocalDate
- getTransportType(): TransportType

### 具体策略
1. JITLockDateStrategy
   - 运输时间≤12小时不锁定
   - 运输时间>12小时锁定当天

2. PO2LockDateStrategy
   - 锁定日期 = 当前日期 + 运输时间 + 1天

## 核心服务
### CallOffCalculationService
主要流程：
1. 处理前一天的叫料单(未入库部分)
2. 按工厂+供应商+物料ID分组
3. 计算每个需求单的锁定日期
4. 处理锁定状态的叫料单
5. 按天处理数据生成叫料计划

## 测试覆盖
- 不同运输类型的锁定规则
- 分组处理逻辑
- 虚拟需求生成
- 异常处理场景

# 名词解释
## PO1
“第一采购订单”或“主采购订单”，是客户向供应商发出的第一份正式订单

📌 特点：
包含合同总数量、交货周期等信息
通常是框架协议下的总订单
后续可能根据实际需求拆分为多个 PO2 或 Call-off

💡 应用场景举例：
整车厂与一级供应商签订年度框架采购协议，其中包含全年预估用量和价格条款。
PO1 不一定是一次性全部交付，而是作为后续分批交货的基础。

## PO2
第二采购订单，基于 PO1 的具体执行订单，用于实际交货。PO2 是基于 PO1 的细分订单，用于具体执行阶段的采购指令。

📌 特点：
是对 PO1 的进一步细化
包含具体的发货时间、数量、地点等信息
更贴近实际生产和物流操作

💡 应用场景举例：
某个工厂本周需要某零部件 500 件，于是从 PO1 中生成一个 PO2 来通知供应商具体发货安排。
在 JIT 系统中，PO2 可能每天甚至每班次生成一次。

## JIT
最小库存，即时叫料

📌 核心思想：
物料只在需要的时候送达生产线
避免库存积压、减少浪费
强调流程的高效协同和精准调度

⚙️ 在叫料计划中的体现：
JIT 可能意味着车间通过“call-off”机制向仓库或供应商发出即时取料请求，确保物料在需要时刚好到达。

## 叫料计划
PO2和JIT需要生成叫料计划单


# 取数逻辑
## 查询（无需实现）
### 1. 计划订单信息
- SAP接口，接口编号: SAP-ZMPSI17
- 入参：2025.1.1到当前日期的所有订单
- 过滤条件：
    - 上线时间<=30天
    - 订单类型：计划订单+生产订单
- 主要字段：
    - 销售订单号+行项目
    - 生产订单号
    - 订单类型
    - 上线时间

### 2. 物料信息
- ATP接口，接口编号：DS_ATP-WLQTDJ
- 参数：订单号
- 过滤条件：
    - 使用类型（USE_TYPE_NAME）：采购订单
- 主要字段：
    - 物料ID
    - 供应商
    - 采购组织
    - 使用类型
    - 需求日期

### 3. 送货方式信息
- SRM接口，接口编号：SAP-ZMMFU1076
- 参数：采购组织+供应商+物料ID
- 过滤条件：
    - 交货方式（ZSHMS）：PO2+JIT
    - 需求日期（MATERIAL_NEED_DATE）：JIT<=7;PO2<=14
- 主要字段：
    - 交货方式

### 4. 运输信息
- 谁提供的接口: **缺失**
- 参数：采购组织+供应商
- 过滤条件：
    - 无
- 主要字段：
    - 运输时间

## 计算
### 1. 锁定
- 锁定逻辑：
    - 锁定天数
        - PO2:
            - 运输时间 + 1
            - 锁定日期 = 当前日期 + 锁定天数
        - JIT:
            - 运输时间<=12x小时 -> 不锁定
            - 运输时间>12x小时 -> 锁定当天

## 计算逻辑
### 实体对象
#### 需求单
- 订单号
- 需求日期（LocalDate）
- 实际需求日期（LocalDate）
- 物料ID
- 供应商
- 工厂编号
- 需求数量
- 运输方式（PO2/JIT枚举）
- 运输时间（PO2-天/JIT-小时）
- 锁定日期（比如2025-06-08，则今天2025-06-04~2025-06-07的叫料数量不允许变更）
#### 叫料单
- 叫料日期（LocalDate）
- 叫料数量
- 订单号
- 需求日期（LocalDate）
- 物料ID
- 供应商
- 工厂编号
- 是否锁定
- 入库数量

### 计算逻辑
1. 获取所有的需求单
2. 获取所有的叫料单（需求日期>=今天）
3. 获取前一天的叫料单并查询入库数量，如果入库数量<叫料数量，则创建一个虚拟的需求单，需求日期设置成今天
4. 需求单和叫料单都按工厂+供应商+物料ID分组计算，互不干扰
5. 对每个分组单独计算
6. 需求单按需求日期排序，叫料单按叫料日期排序
7. 根据运输方式和运输时间计算锁定日期
8. 根据锁定时期筛选叫料单，锁定日期<=今天的数据不允许变更，删除非锁定状态的叫料单
9. 按天处理数据，从今天开始到需求日期最后一天截至，当前日期n
10. 获取需求单列表Rn，n是需求日期，计算需求数量综合TotalRn
11. 查询当前是否存在锁定的叫料单Cn，n是叫料日期
    1. 如果存在
        1. 计算差额Dn=TotalRn-Cn
    2. 如果不存在
        1. 添加一条叫料单，Cn=TotalRn，Dn=0
12. 如果差额Dn不为0，则创建一个虚拟需求单，需求日期=Rn+1，需求数量=Dn，实际需求日期=Rn+1
13. 重复步骤9~12，直到Rn不存在或者剩下的所有Rn都<=0

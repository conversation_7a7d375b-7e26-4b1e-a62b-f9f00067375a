# blysin-test 项目架构

## 类图
```mermaid
classDiagram
    class DemandOrder {
        +String orderNo
        +LocalDate demandDate
        +String materialId
        +String supplier
        +String factoryCode
        +TransportType transportType
        +Integer transportTime
        +LocalDate lockDate
    }

    class CallOffOrder {
        +LocalDate callOffDate
        +Integer callOffQuantity
        +String orderNo
        +Boolean locked
        +Integer receivedQuantity
    }

    class TransportType {
        <<enumeration>>
        PO2
        JIT
    }

    class LockDateStrategy {
        <<interface>>
        +calculateLockDate(DemandOrder, LocalDate) LocalDate
        +getTransportType() TransportType
    }

    class JITLockDateStrategy {
        +int lockThreshold
        +calculateLockDate(DemandOrder, LocalDate) LocalDate
        +getTransportType() TransportType
    }

    class PO2LockDateStrategy {
        +calculateLockDate(DemandOrder, LocalDate) LocalDate
        +getTransportType() TransportType
    }

    class LockDateStrategyFactory {
        -Map<TransportType, LockDateStrategy> strategyMap
        +getStrategy(TransportType) LockDateStrategy
    }

    class CallOffCalculationService {
        -LockDateStrategyFactory lockDateStrategyFactory
        +calculateCallOffPlan(List<DemandOrder>, List<CallOffOrder>, LocalDate) List<CallOffOrder>
    }

    DemandOrder "1" -- "1" TransportType
    CallOffOrder "1" -- "1" DemandOrder
    LockDateStrategyFactory --> LockDateStrategy
    JITLockDateStrategy ..|> LockDateStrategy
    PO2LockDateStrategy ..|> LockDateStrategy
    CallOffCalculationService --> LockDateStrategyFactory
```

## 核心流程
```mermaid
sequenceDiagram
    participant Client
    participant CallOffCalculationService
    participant LockDateStrategyFactory
    participant Strategy

    Client->>CallOffCalculationService: calculateCallOffPlan(demandOrders, callOffOrders, currentDate)
    CallOffCalculationService->>CallOffCalculationService: 处理前一天叫料单
    CallOffCalculationService->>CallOffCalculationService: 按工厂+供应商+物料ID分组
    loop 每个分组
        CallOffCalculationService->>LockDateStrategyFactory: getStrategy(transportType)
        LockDateStrategyFactory-->>CallOffCalculationService: strategy
        CallOffCalculationService->>Strategy: calculateLockDate(demandOrder, currentDate)
        Strategy-->>CallOffCalculationService: lockDate
        CallOffCalculationService->>CallOffCalculationService: 处理锁定状态
        CallOffCalculationService->>CallOffCalculationService: 按天生成叫料计划
    end
    CallOffCalculationService-->>Client: List<CallOffOrder>
```
